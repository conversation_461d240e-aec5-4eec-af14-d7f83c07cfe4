"use client";

import { useState } from "react";

import { ReportDetailResponse } from "@/types/report";

export function useReportDetail() {
  const [reportDetail, setReportDetail] = useState<ReportDetailResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchReportDetail = async (id: string) => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate network delay

      const mockReportDetails: Record<string, ReportDetailResponse> = {
        "1": {
          id: 1,
          name: "Reporte de Campos Informativos",
          description: "Reporte completo de todos los campos informativos del proyecto",
          document_name: "campos_informativos_reporte",
          use_english_fields: false,
          include_observations: true,
          is_active: true,
          created_at: "2024-01-15T10:30:00Z",
          updated_at: "2024-01-20T14:45:00Z",
          full_definition: {
            fields: [
              {
                id: 1,
                name: "Nombre del Cliente",
                type: "informative",
                subphase: {
                  id: 1,
                  name: "Configuración Inicial",
                  phase: {
                    id: 1,
                    name: "Start"
                  }
                }
              },
              {
                id: 2,
                name: "Tipo de Implementación",
                type: "selection",
                subphase: {
                  id: 1,
                  name: "Configuración Inicial",
                  phase: {
                    id: 1,
                    name: "Start"
                  }
                }
              },
              {
                id: 3,
                name: "Documentación Técnica",
                type: "document",
                subphase: {
                  id: 2,
                  name: "Análisis de Requerimientos",
                  phase: {
                    id: 2,
                    name: "Collection"
                  }
                }
              }
            ],
            filters: [
              {
                field_type: "informative",
                filter_option: "completed"
              },
              {
                field_type: "selection",
                filter_option: "in_progress"
              },
              {
                field_type: "document",
                filter_option: "not_completed"
              }
            ]
          }
        },
        "2": {
          id: 2,
          name: "Reporte de Tareas Completadas",
          description: "Seguimiento de tareas completadas por fase",
          document_name: "tareas_completadas",
          use_english_fields: true,
          include_observations: false,
          is_active: true,
          created_at: "2024-01-10T09:15:00Z",
          updated_at: "2024-01-18T16:20:00Z",
          full_definition: {
            fields: [
              {
                id: 4,
                name: "Task Validation",
                type: "task",
                subphase: {
                  id: 3,
                  name: "Development Phase",
                  phase: {
                    id: 3,
                    name: "Migration"
                  }
                }
              },
              {
                id: 5,
                name: "Quality Assurance",
                type: "task_with_subtasks",
                subphase: {
                  id: 4,
                  name: "Testing Phase",
                  phase: {
                    id: 4,
                    name: "Test"
                  }
                }
              }
            ],
            filters: [
              {
                field_type: "task",
                filter_option: "completed"
              },
              {
                field_type: "task_with_subtasks",
                filter_option: "completed"
              }
            ]
          }
        }
      };

      const reportDetail = mockReportDetails[id] || {
        id: parseInt(id),
        name: "Reporte Genérico",
        description: "Descripción del reporte",
        document_name: "reporte_generico",
        use_english_fields: false,
        include_observations: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        full_definition: {
          fields: [],
          filters: []
        }
      };

      setReportDetail(reportDetail);
      return reportDetail;
    } catch (err: any) {
      setError(err.message || "Failed to fetch report detail");
    } finally {
      setLoading(false);
    }
  };

  const clearReportDetail = () => {
    setReportDetail(null);
    setError(null);
  };

  return {
    reportDetail,
    loading,
    error,
    fetchReportDetail,
    clearReportDetail,
  };
}
