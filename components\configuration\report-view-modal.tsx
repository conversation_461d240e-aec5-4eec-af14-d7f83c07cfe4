import React, { useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  TableHeader,
  TableColumn,
  TableBody,
  TableRow,
  TableCell,
  Card,
  CardBody,
  CardHeader,
  Divider,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import { useReportDetail } from "@/hooks/reports/useReportDetail";
import { ApiReport } from "@/types/report";

interface ReportViewModalProps {
  isOpen: boolean;
  report: ApiReport | null;
  onClose: () => void;
}

const getFieldTypeDisplayName = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "Informativo";
    case "selection":
      return "Selección";
    case "task":
      return "Tarea";
    case "document":
      return "Documento";
    case "task_with_subtasks":
      return "Subtarea";
    default:
      return fieldType;
  }
};

const getFieldTypeIcon = (fieldType: string) => {
  switch (fieldType) {
    case "informative":
      return "heroicons:information-circle";
    case "selection":
      return "heroicons:list-bullet";
    case "task":
      return "heroicons:check-circle";
    case "document":
      return "heroicons:document";
    case "task_with_subtasks":
      return "heroicons:squares-plus";
    default:
      return "heroicons:question-mark-circle";
  }
};

const getFilterDisplayName = (filterOption: string) => {
  switch (filterOption) {
    case "completed":
      return "Completado";
    case "not_completed":
      return "No completado";
    case "in_progress":
      return "En progreso";
    default:
      return filterOption;
  }
};

const getFilterDotColor = (filterOption: string) => {
  switch (filterOption) {
    case "completed":
      return "bg-success";
    case "not_completed":
      return "bg-danger";
    case "in_progress":
      return "bg-warning";
    default:
      return "bg-default";
  }
};

export function ReportViewModal({
  isOpen,
  report,
  onClose,
}: ReportViewModalProps) {
  const {
    reportDetail,
    loading,
    error,
    fetchReportDetail,
    clearReportDetail,
  } = useReportDetail();

  useEffect(() => {
    if (isOpen && report) {
      fetchReportDetail(report.id.toString());
    } else if (!isOpen) {
      clearReportDetail();
    }
  }, [isOpen, report]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const renderConfiguration = () => {
    if (!reportDetail) return null;

    return (
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Icon icon="heroicons:cog-6-tooth" width={20} />
            <h4 className="text-lg font-semibold">Configuración</h4>
          </div>
        </CardHeader>
        <CardBody className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-sm text-default-600">Nombre del documento:</span>
            <span className="font-medium">{reportDetail.document_name}</span>
          </div>
          <Divider />
          <div className="flex justify-between items-center">
            <span className="text-sm text-default-600">Idioma de campos:</span>
            <Chip 
              color={reportDetail.use_english_fields ? "secondary" : "primary"} 
              size="sm" 
              variant="flat"
            >
              {reportDetail.use_english_fields ? "Inglés" : "Español"}
            </Chip>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-sm text-default-600">Observaciones:</span>
            <Chip 
              color={reportDetail.include_observations ? "success" : "default"} 
              size="sm" 
              variant="flat"
            >
              {reportDetail.include_observations ? "Incluidas" : "No incluidas"}
            </Chip>
          </div>
        </CardBody>
      </Card>
    );
  };

  const renderFields = () => {
    if (!reportDetail?.full_definition?.fields) {
      return (
        <div className="text-center py-8">
          <p className="text-default-500">No hay campos configurados</p>
        </div>
      );
    }

    return (
      <Table aria-label="Campos del reporte">
        <TableHeader>
          <TableColumn>CAMPO</TableColumn>
          <TableColumn>TIPO</TableColumn>
          <TableColumn>FASE</TableColumn>
          <TableColumn>SUBFASE</TableColumn>
        </TableHeader>
        <TableBody>
          {reportDetail.full_definition.fields.map((field) => (
            <TableRow key={field.id}>
              <TableCell>
                <div className="flex items-center gap-2">
                  <Icon
                    icon={getFieldTypeIcon(field.type)}
                    width={16}
                    className="text-default-500"
                  />
                  <span className="font-medium">{field.name}</span>
                </div>
              </TableCell>
              <TableCell>
                <Chip size="sm" variant="flat">
                  {getFieldTypeDisplayName(field.type)}
                </Chip>
              </TableCell>
              <TableCell>{field.subphase.phase.name}</TableCell>
              <TableCell>{field.subphase.name}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    );
  };

  const renderFilters = () => {
    if (!reportDetail?.full_definition?.filters) {
      return (
        <div className="text-center py-8">
          <p className="text-default-500">No hay filtros configurados</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        {reportDetail.full_definition.filters.map((filter, index) => (
          <Card key={index}>
            <CardBody>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Icon
                    icon={getFieldTypeIcon(filter.field_type)}
                    width={20}
                    className="text-primary"
                  />
                  <div>
                    <h5 className="font-semibold">
                      {getFieldTypeDisplayName(filter.field_type)}
                    </h5>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className={`w-2 h-2 rounded-full ${getFilterDotColor(filter.filter_option)}`} />
                  <span className="font-medium">
                    {getFilterDisplayName(filter.filter_option)}
                  </span>
                </div>
              </div>
            </CardBody>
          </Card>
        ))}
      </div>
    );
  };

  if (!report) return null;

  return (
    <Modal
      isOpen={isOpen}
      scrollBehavior="inside"
      size="5xl"
      onClose={onClose}
    >
      <ModalContent>
        {(onClose) => (
          <>
            <ModalHeader className="flex flex-col gap-1 pb-2 border-b">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-xl font-medium">{report.name}</h3>
                  {report.description && (
                    <p className="text-small text-default-500 mt-1">
                      {report.description}
                    </p>
                  )}
                </div>
                <Chip
                  color={report.is_active ? "success" : "default"}
                  size="sm"
                  variant="flat"
                >
                  {report.is_active ? "Activo" : "Inactivo"}
                </Chip>
              </div>
              {reportDetail && (
                <div className="flex gap-4 text-xs text-default-500 mt-2">
                  <span>Creado: {formatDate(reportDetail.created_at)}</span>
                  <span>
                    Actualizado: {formatDate(reportDetail.updated_at)}
                  </span>
                </div>
              )}
            </ModalHeader>
            <ModalBody className="py-4">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : error ? (
                <div className="text-center py-8">
                  <p className="text-danger">{error}</p>
                </div>
              ) : (
                <Tabs aria-label="Detalles del reporte" className="w-full">
                  <Tab
                    key="config"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="heroicons:cog-6-tooth" />
                        <span>Configuración</span>
                      </div>
                    }
                  >
                    <div className="mt-4">{renderConfiguration()}</div>
                  </Tab>
                  <Tab
                    key="fields"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="heroicons:squares-2x2" />
                        <span>
                          Campos (
                          {reportDetail?.full_definition?.fields?.length || 0})
                        </span>
                      </div>
                    }
                  >
                    <div className="mt-4">{renderFields()}</div>
                  </Tab>
                  <Tab
                    key="filters"
                    title={
                      <div className="flex items-center space-x-2">
                        <Icon icon="heroicons:funnel" />
                        <span>
                          Filtros (
                          {reportDetail?.full_definition?.filters?.length || 0})
                        </span>
                      </div>
                    }
                  >
                    <div className="mt-4">{renderFilters()}</div>
                  </Tab>
                </Tabs>
              )}
            </ModalBody>
            <ModalFooter>
              <Button variant="light" onPress={onClose}>
                Cerrar
              </Button>
            </ModalFooter>
          </>
        )}
      </ModalContent>
    </Modal>
  );
}
