"use client";

import { useState } from "react";

import { ApiReport, CreateReportData, UpdateReportData } from "@/types/report";

export function useReports() {
  const [reports, setReports] = useState<ApiReport[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate network delay

      const mockReports: ApiReport[] = [
        {
          id: 1,
          name: "Reporte de Campos Informativos",
          description:
            "Reporte completo de todos los campos informativos del proyecto",
          document_name: "campos_informativos_reporte",
          use_english_fields: false,
          include_observations: true,
          is_active: true,
          created_at: "2024-01-15T10:30:00Z",
          updated_at: "2024-01-20T14:45:00Z",
        },
        {
          id: 2,
          name: "Reporte de Tareas Completadas",
          description: "Seguimiento de tareas completadas por fase",
          document_name: "tareas_completadas",
          use_english_fields: true,
          include_observations: false,
          is_active: true,
          created_at: "2024-01-10T09:15:00Z",
          updated_at: "2024-01-18T16:20:00Z",
        },
        {
          id: 3,
          name: "Reporte de Documentos Pendientes",
          description: "Lista de documentos que requieren atención",
          document_name: "documentos_pendientes",
          use_english_fields: false,
          include_observations: true,
          is_active: false,
          created_at: "2024-01-05T11:00:00Z",
          updated_at: "2024-01-12T13:30:00Z",
        },
        {
          id: 4,
          name: "Reporte General de Progreso",
          description: "Vista general del progreso del proyecto por fases",
          document_name: "progreso_general",
          use_english_fields: false,
          include_observations: true,
          is_active: true,
          created_at: "2024-01-08T08:45:00Z",
          updated_at: "2024-01-22T10:15:00Z",
        },
      ];

      setReports(mockReports);
    } catch (err: any) {
      setError(err.message || "Failed to fetch reports");
    } finally {
      setLoading(false);
    }
  };

  const createReport = async (
    reportData: CreateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 1500)); // Simulate network delay

      const newReport: ApiReport = {
        id: Date.now(), // Use timestamp as mock ID
        name: reportData.reportConfig.name,
        description: reportData.reportConfig.description,
        document_name: reportData.reportConfig.documentName,
        use_english_fields: reportData.reportConfig.useEnglishFields,
        include_observations: reportData.reportConfig.includeObservations,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Update reports state with the new report
      setReports((prevReports) => [...prevReports, newReport]);

      return { success: true, data: newReport };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to create report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const updateReport = async (
    id: number,
    updateData: UpdateReportData,
  ): Promise<{ success: boolean; data?: ApiReport; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 1200)); // Simulate network delay

      // Update reports state
      setReports((prevReports) =>
        prevReports.map((report) => {
          if (report.id === id) {
            return {
              ...report,
              name: updateData.name || report.name,
              description: updateData.description || report.description,
              document_name: updateData.document_name || report.document_name,
              use_english_fields:
                updateData.use_english_fields ?? report.use_english_fields,
              include_observations:
                updateData.include_observations ?? report.include_observations,
              updated_at: new Date().toISOString(),
            };
          }

          return report;
        }),
      );

      const updatedReport = reports.find((r) => r.id === id);

      return { success: true, data: updatedReport };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to update report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  const deleteReport = async (
    id: number,
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call with mock data
      await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate network delay

      // Remove report from state
      setReports((prevReports) =>
        prevReports.filter((report) => report.id !== id),
      );

      return { success: true };
    } catch (err: any) {
      const errorMsg = err.message || "Failed to delete report";

      setError(errorMsg);

      return { success: false, error: errorMsg };
    } finally {
      setLoading(false);
    }
  };

  return {
    reports,
    loading,
    error,
    fetchReports,
    createReport,
    updateReport,
    deleteReport,
  };
}
